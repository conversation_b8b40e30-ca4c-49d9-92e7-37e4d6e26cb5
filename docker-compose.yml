services:
  eko-tenant-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: eko-tenant-frontend
    network_mode: host
    environment:
      - NODE_ENV=production
      - PORT=${PORT:-8207}
      - TZ=${TZ:-UTC}
    env_file:
      - .env
    volumes:
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    labels:
      - "com.project.name=eko-tenant-frontend"
