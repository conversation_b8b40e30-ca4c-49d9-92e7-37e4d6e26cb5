pipeline {
    agent any

    environment {
        APP_DIR = "${env.WORKSPACE}"
    }

    stages {
        stage('Checkout & Cleanup') {
            steps {
                dir("${APP_DIR}") {
                    echo "🧹 Cleaning up previous builds..."
                    sh '''
                        # Clean up any existing containers and images
                        docker compose down --remove-orphans || true
                        docker system prune -f || true
                    '''
                }
            }
        }

        stage('Set Up .env File') {
            steps {
                dir("${APP_DIR}") {
                    withCredentials([file(credentialsId: 'eko_tenant_frontend', variable: 'ENV_FILE')]) {
                        sh '''
                            echo "📄 Copying .env file..."
                            cp "$ENV_FILE" .env
                            chmod 600 .env
                        '''
                    }
                }
            }
        }

        stage('Docker Build') {
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏗 Building Docker images..."
                            sh 'docker compose --env-file .env build --no-cache'
                            env.BUILD_SUCCEEDED = "true"
                        } catch (Exception err) {
                            echo "❌ Build failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            slackSend channel: '#deployments', message: "❌ EKO Tenant Frontend Build failed: ${env.JOB_NAME}"
                            env.BUILD_SUCCEEDED = "false"
                        }
                    }
                }
            }
        }

        stage('Deploy') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🚀 Deploying EKO Tenant Frontend..."
                            sh 'docker compose --env-file .env up -d --force-recreate --no-deps'

                            echo "✅ Deployment successful"
                            slackSend channel: '#deployments', message: "✅ EKO Tenant Frontend deployed successfully: ${env.JOB_NAME}"
                        } catch (Exception err) {
                            echo "❌ Deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            slackSend channel: '#deployments', message: "❌ EKO Tenant Frontend deployment failed: ${env.JOB_NAME}"
                            throw err
                        }
                    }
                }
            }
        }

        stage('Health Check') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏥 Performing health check..."
                            sh '''
                                # Wait for container to be ready
                                sleep 10

                                # Check if container is running
                                if docker compose ps | grep -q "eko-tenant-frontend.*Up"; then
                                    echo "✅ Container is running"
                                else
                                    echo "❌ Container is not running"
                                    exit 1
                                fi

                                # Check if port is accessible
                                if curl -f http://localhost:8207 > /dev/null 2>&1; then
                                    echo "✅ Application is responding on port 8207"
                                else
                                    echo "⚠️ Application may still be starting up"
                                fi
                            '''
                        } catch (Exception err) {
                            echo "⚠️ Health check failed: ${err}"
                            // Don't fail the build for health check issues
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ EKO Tenant Frontend pipeline completed successfully!'
            slackSend channel: '#deployments',
                     color: 'good',
                     message: "✅ EKO Tenant Frontend pipeline completed successfully!\nJob: ${env.JOB_NAME}\nBuild: ${env.BUILD_NUMBER}"
        }
        failure {
            echo '❌ EKO Tenant Frontend pipeline failed!'
            slackSend channel: '#deployments',
                     color: 'danger',
                     message: "❌ EKO Tenant Frontend pipeline failed!\nJob: ${env.JOB_NAME}\nBuild: ${env.BUILD_NUMBER}\nCheck logs for details."
        }
        always {
            echo "📌 EKO Tenant Frontend pipeline finished."
            // Clean up workspace if needed
            cleanWs(cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    notFailBuild: true,
                    patterns: [[pattern: '.gitignore', type: 'INCLUDE'],
                              [pattern: '.propsfile', type: 'EXCLUDE']])
        }
    }
}